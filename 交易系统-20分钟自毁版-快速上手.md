# 交易系统-20分钟自毁版-快速上手

## 🎯 只需4步，轻松搞定！

### 第1步：安装Docker
- **Windows**: 下载 [Docker Desktop](https://www.docker.com/products/docker-desktop/) 并安装
- **Linux**: 运行 `curl -fsSL https://get.docker.com | sh`
- **macOS**: 下载 [Docker Desktop](https://www.docker.com/products/docker-desktop/) 并安装

### 第2步：创建文件夹
```bash
# Windows PowerShell
New-Item -ItemType Directory -Force -Path "C:\TradingSystem"
cd "C:\TradingSystem"

# Linux/macOS
mkdir ~/TradingSystem && cd ~/TradingSystem
```

### 第3步：导入镜像
```bash
# 把 交易系统-20分钟自毁版-v1.0.tar 文件放到当前目录
docker load -i "交易系统-20分钟自毁版-v1.0.tar"
```

**验证导入结果：**
```bash
# 查看导入的镜像
docker images
```

### 第4步：启动系统
```bash
# ⚠️ 注意：实际的镜像名称可能是以下之一，请根据第3步的结果选择
docker run -d --rm -p 8080:80 --name trading-system trading-system-temp-20min:latest
# 或者
docker run -d --rm -p 8080:80 --name trading-system trading-system-no-limits:v5.02
```

**检查启动状态：**
```bash
docker ps
```

## 🌐 访问系统
打开浏览器访问: **http://localhost:8080**

## ⏰ 重要提醒
- 系统会在**20分钟后自动自毁**（仅限自毁版本）
- 到期后页面会自动跳转到联系页面
- 联系方式: [@handouT00T](https://t.me/handouT00T)

## 🔧 常用命令
```bash
# 查看运行状态
docker ps

# 查看日志
docker logs trading-system

# 停止系统
docker stop trading-system
```

## ❓ 遇到问题？

### 常见问题1：镜像名称不匹配
**现象**：`docker run` 时提示镜像不存在
**解决**：
1. 运行 `docker images` 查看实际导入的镜像名称
2. 使用正确的镜像名称启动容器

### 常见问题2：端口被占用
**现象**：`port is already allocated`
**解决**：把命令中的 `8080` 改成 `8081`

### 常见问题3：无法访问
**现象**：浏览器无法打开页面
**解决**：
1. 检查Docker是否启动：`docker ps`
2. 检查容器日志：`docker logs trading-system`
3. 尝试其他端口：如8081

### 常见问题4：Redis服务问题
**现象**：日志显示Redis启动失败
**解决**：这是已知问题，不影响前端访问，可以正常使用

## 🆘其他问题
联系 [@handouT00T](https://t.me/handouT00T)

---

**搞定！就这么简单！🎉** 