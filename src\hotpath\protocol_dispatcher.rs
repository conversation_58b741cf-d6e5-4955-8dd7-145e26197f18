use anyhow::Result;
use std::sync::Arc;
use tracing::{debug, error, info};
use arc_swap::ArcSwap;
use crate::hotpath::filter::Filter;
use crate::hotpath::redis_subscriber::RedisSubscriber;

/// 协议类型枚举
#[derive(Debug, Clone, PartialEq)]
pub enum ProtocolType {
    Pump,
    Bonk,
    Unknown,
}

/// 高性能协议分发器
/// 负责检测协议类型并将消息分发到对应的处理器
pub struct ProtocolDispatcher {
    /// Pump处理器 - 复用现有RedisSubscriber
    pump_handler: Arc<RedisSubscriber>,
    /// Bonk处理器 - 新建的处理器（暂时为空，后续实现）
    // bonk_handler: Arc<BonkProcessor>,
}

impl ProtocolDispatcher {
    /// 创建新的协议分发器
    pub fn new(pump_handler: Arc<RedisSubscriber>) -> Self {
        info!("初始化协议分发器");
        Self {
            pump_handler,
            // bonk_handler,
        }
    }
    
    /// 高性能协议检测
    /// 使用字节级匹配，避免UTF-8转换开销，实现微秒级性能
    pub fn detect_protocol(&self, payload: &[u8]) -> ProtocolType {
        if payload.len() < 20 {
            return ProtocolType::Unknown;
        }
        
        // 使用字节窗口匹配，避免字符串转换
        // Bonk特征：包含"pool_state"和"pool_base_vault"
        if payload.windows(10).any(|w| w == b"pool_state") &&
           payload.windows(15).any(|w| w == b"pool_base_vault") {
            debug!("检测到Bonk协议数据");
            return ProtocolType::Bonk;
        }
        
        // 默认为Pump协议，保持向后兼容
        debug!("检测到Pump协议数据");
        ProtocolType::Pump
    }
    
    /// 分发消息到对应的协议处理器
    pub async fn dispatch_message(
        &self,
        payload: &[u8],
        filter: &Arc<ArcSwap<Filter>>,
    ) -> Result<()> {
        let protocol = self.detect_protocol(payload);
        
        match protocol {
            ProtocolType::Bonk => {
                debug!("分发Bonk消息到Bonk处理器");
                // TODO: 实现bonk处理器后启用
                // self.bonk_handler.process_bonk_data(payload, filter).await
                info!("检测到Bonk数据，暂时跳过处理");
                Ok(())
            }
            ProtocolType::Pump | ProtocolType::Unknown => {
                debug!("分发Pump消息到现有处理器");
                // 直接调用现有RedisSubscriber的内部处理方法
                self.pump_handler.process_pump_message_internal(payload, filter).await
            }
        }
    }
}
