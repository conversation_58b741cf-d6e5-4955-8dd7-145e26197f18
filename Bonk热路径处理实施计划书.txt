Bonk热路径处理重新布局方案
一、架构重新设计原则
1. 协议分离原则
每个协议有独立的处理模块
避免在现有文件中堆积代码
保持清晰的模块边界
2. 功能模块化原则
买入、卖出、解析等功能独立模块
便于维护和扩展
代码职责单一
二、新的目录结构设计
核心协议目录

src/hotpath/protocols/
├── pump/
│   ├── mod.rs
│   ├── parser.rs          # 现有parser.rs移入
│   ├── transaction_builder.rs  # 现有transaction_builder.rs移入
│   └── types.rs           # pump特定类型
└── bonk/
    ├── mod.rs
    ├── parser.rs          # bonk数据解析
    ├── transaction_builder.rs  # bonk交易构建
    └── types.rs           # bonk特定类型
	
	
	服务层重构
	
	src/services/executors/
├── mod.rs
├── pump_executor.rs       # pump买卖执行器
├── bonk_executor.rs       # bonk买卖执行器
└── common.rs             # 通用执行逻辑

处理器层
src/hotpath/processors/
├── mod.rs
├── pump_processor.rs      # pump主处理器
└── bonk_processor.rs      # bonk主处理器


三、重新调整的实施计划
阶段一：代码重构和目录整理（优先级：最高）
1.1 创建协议目录结构
位置: src/hotpath/protocols/
操作:
创建pump和bonk子目录
移动现有parser.rs到pump/parser.rs
移动现有transaction_builder.rs到pump/transaction_builder.rs
更新所有import路径
1.2 重构服务层
位置: src/services/executors/
操作:
创建executors目录
将sell_executor.rs重构为pump_executor.rs
提取通用逻辑到common.rs
为bonk创建独立的bonk_executor.rs
1.3 创建处理器层
位置: src/hotpath/processors/
操作:
从redis_subscriber.rs中提取pump处理逻辑到pump_processor.rs
创建bonk_processor.rs
redis_subscriber.rs只负责分发
阶段二：Bonk协议实现（优先级：高）
2.1 Bonk数据层
位置: src/hotpath/protocols/bonk/
文件:
types.rs: BonkTrade等数据结构
parser.rs: bonk数据解析逻辑
2.2 Bonk交易构建层
位置: src/hotpath/protocols/bonk/
文件:
transaction_builder.rs: Raydium交易构建
独立的PDA计算和指令构建
2.3 Bonk执行器
位置: src/services/executors/
文件:
bonk_executor.rs: bonk买卖执行逻辑
复用common.rs中的通用逻辑
阶段三：处理器集成（优先级：中）
3.1 Pump处理器
位置: src/hotpath/processors/pump_processor.rs
内容:
从redis_subscriber.rs提取的pump处理逻辑
使用pump协议模块
使用pump执行器
3.2 Bonk处理器
位置: src/hotpath/processors/bonk_processor.rs
内容:
完整的bonk处理流程
使用bonk协议模块
使用bonk执行器
3.3 Redis订阅器简化
位置: src/hotpath/redis_subscriber.rs
内容:
只负责协议检测和分发
调用对应的处理器
移除具体的处理逻辑
阶段四：配置和集成（优先级：低）
4.1 主程序集成
位置: src/main.rs
内容:
初始化两个处理器
设置分发通道
启动后台任务
4.2 API和监控扩展
位置: 各相关服务文件
内容:
支持协议区分的监控
API接口扩展


src/
├── hotpath/
│   ├── protocols/
│   │   ├── mod.rs
│   │   ├── pump/
│   │   │   ├── mod.rs
│   │   │   ├── parser.rs          # 从hotpath/parser.rs移入
│   │   │   ├── transaction_builder.rs  # 从hotpath/transaction_builder.rs移入
│   │   │   └── types.rs           # pump特定类型定义
│   │   └── bonk/
│   │       ├── mod.rs
│   │       ├── parser.rs          # bonk数据解析
│   │       ├── transaction_builder.rs  # bonk交易构建
│   │       └── types.rs           # bonk特定类型定义
│   ├── processors/
│   │   ├── mod.rs
│   │   ├── pump_processor.rs      # pump主处理器
│   │   └── bonk_processor.rs      # bonk主处理器
│   ├── filter.rs                  # 保持不变，扩展协议支持
│   ├── calculator.rs              # 保持不变
│   ├── redis_subscriber.rs        # 简化为分发器
│   └── mod.rs
├── services/
│   ├── executors/
│   │   ├── mod.rs
│   │   ├── common.rs              # 通用执行逻辑
│   │   ├── pump_executor.rs       # pump买卖执行器
│   │   └── bonk_executor.rs       # bonk买卖执行器
│   └── ... (其他服务保持不变)
└── shared/
    ├── pda.rs                     # 扩展支持raydium PDA
    └── ... (其他共享模块)
	
	
	
	五、重构优势
1. 清晰的职责分离
每个协议有独立的命名空间
买卖逻辑不再混在一个文件中
处理器专注于流程编排
2. 易于维护和扩展
新增协议只需添加新的协议目录
修改某个协议不影响其他协议
代码结构清晰，便于理解
3. 更好的测试支持
每个模块可以独立测试
协议特定的测试用例分离
便于mock和单元测试
4. 性能优化空间
可以针对不同协议进行特定优化
避免不必要的代码路径
更好的编译时优化
六、迁移策略
第一步：创建新结构（不破坏现有功能）
创建新的目录结构
复制现有代码到新位置
更新import路径
确保编译通过
第二步：重构现有代码
提取pump处理逻辑到pump_processor.rs
简化redis_subscriber.rs
重构sell_executor.rs为pump_executor.rs
第三步：实现bonk支持
实现bonk协议模块
实现bonk处理器和执行器
集成到主程序
第四步：清理和优化
删除旧文件
优化性能
完善文档