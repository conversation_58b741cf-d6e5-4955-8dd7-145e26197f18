/// 交易分发器模块
/// 
/// 负责订阅Redis，解析交易，然后分发给对应的协议处理器

use anyhow::{anyhow, Result};
use futures_util::stream::StreamExt;
use tracing::{info, debug, error};
use std::sync::Arc;
use tokio::sync::mpsc;

use crate::protocols::pump::parse_trades_from_redis_bytes;
use crate::shared::types::HotPathTrade;
use crate::protocols::pump::TransactionBuilder;
use solana_sdk::pubkey::Pubkey;

const TRADES_CHANNEL: &str = "trades_channel";

/// 交易分发器 - 订阅Redis并分发
pub struct TradeDispatcher {
    redis_url: String,
    user_wallet_pubkey: Pubkey,
    /// 发送给Pump处理器的通道
    pump_tx: mpsc::UnboundedSender<Vec<HotPathTrade>>,
}

impl TradeDispatcher {
    /// 创建新的交易分发器
    pub fn new(
        redis_url: &str,
        transaction_builder: Arc<TransactionBuilder>,
        pump_tx: mpsc::UnboundedSender<Vec<HotPathTrade>>,
    ) -> Self {
        let user_wallet_pubkey = transaction_builder.get_wallet_pubkey();
        
        Self {
            redis_url: redis_url.to_string(),
            user_wallet_pubkey,
            pump_tx,
        }
    }
    
    /// 启动分发器 - 订阅Redis并分发
    pub async fn start(&self) -> Result<()> {
        let client = redis::Client::open(self.redis_url.as_ref())
            .map_err(|e| anyhow!("创建Redis客户端失败: {}", e))?;

        let mut pubsub_conn = client
            .get_async_pubsub()
            .await
            .map_err(|e| anyhow!("获取PubSub连接失败: {}", e))?;

        pubsub_conn
            .subscribe(TRADES_CHANNEL)
            .await
            .map_err(|e| anyhow!("订阅频道 '{}' 失败: {}", TRADES_CHANNEL, e))?;

        info!("交易分发器已成功订阅Redis频道: {}", TRADES_CHANNEL);

        let mut msg_stream = pubsub_conn.on_message();
        
        while let Some(msg) = msg_stream.next().await {
            let payload: Vec<u8> = msg.get_payload_bytes().to_vec();
            
            // 解析交易
            let trades = parse_trades_from_redis_bytes(&payload, &self.user_wallet_pubkey);
            
            if !trades.is_empty() {
                debug!("分发器解析到 {} 笔交易", trades.len());
                
                // 分发逻辑 - 当前只处理pump协议
                for trade in &trades {
                    // 检查是否是pump协议（这里简化处理，实际可以根据需要判断）
                    // 暂时所有交易都认为是pump协议
                    debug!("分发Pump协议交易: {}", trade.signature);
                }
                
                // 发送给Pump处理器
                if let Err(e) = self.pump_tx.send(trades) {
                    error!("发送交易到Pump处理器失败: {}", e);
                }
            }
        }
        
        Ok(())
    }
}